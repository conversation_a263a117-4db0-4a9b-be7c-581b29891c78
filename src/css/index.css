.search-bar {
    flex: 1;
    /* TODO : définir la taille maximale de la barre de recherche */
    max-width: 600px;
    margin: 0 2rem;
}

.search-bar input {
    width: 100%;
    padding: 0.5rem 1rem;
    font-size: 1rem;
}

/* Filtres TODO : Compléter le CSS au besoin*/
.filters {
    margin: 0 auto 2rem auto;
    width: fit-content;
}

.filter-form {
    display: flex;
    gap: 2rem;
    align-items: start;
    justify-content: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

#max-time {
    accent-color: var(--accent);
}

/* Books */
.book-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
    width: 100%;
}

.book-card {
    position: relative;
    padding: 0rem;
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    gap: 1rem;
    background: #fff;
}

.book-card:hover {
    /* TODO : Ajouter un effet de déplacement lors d'un survol */
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.book-card img {
    width: 150px;
    height: 175px;
    object-fit: cover;
}

.book-genre {
    background: var(--genre-background);
    color: var(--genre);
    width: fit-content;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.reading-time {
    font-weight: bold;
    color: var(--time);
    /* TODO : Positionner correctement le temps de lecture en bas à droite */
    position: absolute;
    bottom: 10px;
    right: 10px;
}

/* Media Queries */

/* TODO : Changer le visuel en bas de 800px de largeur */
@media (max-width: 800px) {
    .header-container {
        flex-direction: column;
        gap: 1rem;
    }

    nav ul {
        flex-direction: column;
        align-items: center;
    }

    .book-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .book-card img {
        margin-bottom: 1rem;
    }

    .book-card div > *:not(a) {
        display: none;
    }
}

/* TODO : Changer le visuel entre 600 et 800px de largeur */
@media (max-width: 700px) {
    .search-bar {
        display: none;
    }
}
