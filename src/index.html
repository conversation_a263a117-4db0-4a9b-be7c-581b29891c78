<!DOCTYPE html>
<html lang="fr">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Combien de Temps pour Lire - Accueil</title>
  <link rel="stylesheet" href="css/shared.css">
  <link rel="stylesheet" href="css/index.css">
</head>

<body>
  <header>
    <div class="header-container">
      <a href="index.html" class="logo">Combien de Temps pour Lire</a>
      <!-- TODO : Ajouter la barre de recherche -->
      <div class="search-bar">
        <input type="text" placeholder="Rechercher un livre...">
      </div>
      <nav>
        <ul>
          <!-- TODO : Compléter le menu de navigation -->
          <li><a href="index.html" class="active">Accueil</a></li>
          <li><a href="add.html" >Ajouter</a></li>
          <li><a href="about.html">À propos</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <main>
    <section class="filters rounded-shadow">
      <!-- TODO : Compléter le formulaire -->
      <form class="filter-form">
        <div class="filter-group">
          <label for="max-time">Durée maximale</label>
          <input type="range" id="max-time" min="0" max="2000" value="20">
          <span id="time-display">20 heures</span>
        </div>
        <div class="filter-group">
          <label for="genre">Genre</label>
          <!-- TODO : permete le choix de genre -->
          <select id="genre">
            <option>Tous les genres</option>
            <option>Fiction</option>
            <option>Science-fiction</option>
            <option>Fantasy</option>
            <option>Historique</option>
          </select>
        </div>
      </form>
    </section>

    <section class="book-grid">
      <article class="book-card rounded-shadow">
        <!-- TODO : Compléter la vignette -->
        <img src="images/pprince.jpeg" alt="Couverture Le Petit Prince">
        <div>
          <a href="book.html?id=1"><strong>Le Petit Prince</strong></a>
          <p>Antoine de Saint-Exupéry</p>
          <span class="book-genre">Fiction</span>
          <span class="reading-time">02:30</span>
        </div>
      </article>
      <!-- TODO : Ajouter les 2 autres vignettes -->
      <article class="book-card rounded-shadow">
        <img src="images/mis.jpeg" alt="Couverture Les Misérables">
        <div>
          <a href="book.html?id=2"><strong>Les Misérables</strong></a>
          <p>Victor Hugo</p>
          <span class="book-genre">Historique</span>
          <span class="reading-time">18:45</span>
        </div>
      </article>
      <article class="book-card rounded-shadow">
        <img src="images/mnil.jpg" alt="Couverture Mort sur le Nil">
        <div>
          <a href="book.html?id=3"><strong>Mort sur le Nil</strong></a>
          <p>Agatha Christie</p>
          <span class="book-genre">Mystère</span>
          <span class="reading-time">12:15</span>
        </div>
      </article>
    </section>
  </main>

  <footer>
    <p>&copy; 2025 Combien de Temps pour Lire |
      Projet réalisé par
      <span id="etudiant-1">Aly Abdoulaye-Idriss, 2363387</span> et
      <span id="etudiant-2">Franck Fongang, 2395258</span>
    </p>
  </footer>

  <script>
    const timeSlider = document.getElementById('max-time');
    const timeDisplay = document.getElementById('time-display');

    if (timeSlider && timeDisplay) {
      timeSlider.addEventListener('input', () => {
        timeDisplay.textContent = timeSlider.value + ' heures';
      });
    }
  </script>
</body>

</html>